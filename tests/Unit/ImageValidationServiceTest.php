<?php

use App\Models\ApiData;
use App\Models\ProductVariant;
use Domains\Marketing\GoogleShoppingContent\Services\ImageValidationService;

beforeEach(function () {
    $this->service = new ImageValidationService();
});

test('it identifies marketing assets urls as placeholders', function () {
    $marketingAssetsUrl = 'https://marketing-assets.example.com/image.jpg';
    $regularUrl = 'https://assets.example.com/image.jpg';

    expect($this->service->isMarketingAssetsUrl($marketingAssetsUrl))->toBeTrue();
    expect($this->service->isMarketingAssetsUrl($regularUrl))->toBeFalse();
});

test('it rejects products with no featured image', function () {
    $productVariant = new ProductVariant([
        'featured_image' => null
    ]);

    expect($this->service->hasValidProductImage($productVariant))->toBeFalse();
});

test('it rejects products with replace in featured image', function () {
    $productVariant = new ProductVariant([
        'featured_image' => ['full' => 'https://example.com/replace/image.jpg']
    ]);

    expect($this->service->hasValidProductImage($productVariant))->toBeFalse();
});

// Note: The following tests require database access and factories
// They are commented out until we have proper test data setup

// test('it rejects products with marketing assets original url', function () {
//     $apiData = ApiData::factory()->create([
//         'data' => [
//             'image_url' => 'https://marketing-assets.example.com/placeholder.jpg',
//             'sku' => 'TEST-SKU'
//         ]
//     ]);

//     $productVariant = ProductVariant::factory()->create([
//         'featured_image' => ['full' => 'https://processed.example.com/image.jpg'],
//         'product_variant_provider_id' => $apiData->code
//     ]);

//     expect($this->service->hasValidProductImage($productVariant))->toBeFalse();
// });

// test('it accepts products with valid images', function () {
//     $apiData = ApiData::factory()->create([
//         'data' => [
//             'image_url' => 'https://assets.example.com/real-product.jpg',
//             'sku' => 'TEST-SKU'
//         ]
//     ]);

//     $productVariant = ProductVariant::factory()->create([
//         'featured_image' => ['full' => 'https://processed.example.com/image.jpg'],
//         'product_variant_provider_id' => $apiData->code
//     ]);

//     expect($this->service->hasValidProductImage($productVariant))->toBeTrue();
// });
