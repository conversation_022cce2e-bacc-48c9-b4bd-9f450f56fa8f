{"name": "adi/doorhub", "type": "project", "description": "E-commerce platform revolutionizing procurement of commercial doors, frames and hardware.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "ext-calendar": "*", "ext-json": "*", "ext-pdo": "*", "ext-redis": "*", "algolia/algoliasearch-client-php": "^3.0", "atymic/laravel-bulk-sqs-queue": "^0.4.0", "avalara/avataxclient": "^22.7", "aws/aws-sdk-php": "^3.283", "better-futures-studio/filament-local-logins": "^1.2", "codebar-ag/laravel-prerender": "^11.0", "cweagans/composer-patches": "^1.7", "diglactic/laravel-breadcrumbs": "9.0", "doctrine/dbal": "^3.1", "easypost/easypost-php": "^3.6", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2.83", "filament/spatie-laravel-tags-plugin": "^3.2", "flowframe/laravel-trend": "^0.2.0", "google/apiclient": "^2.12", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^7.2", "hisorange/browser-detect": "^4.5", "hubspot/api-client": "^8.3", "illuminate/console": "^11.9", "kalnoy/nestedset": "^6.0", "lab404/laravel-impersonate": "1.7.5", "laravel/framework": "^11.0", "laravel/horizon": "^5.0", "laravel/prompts": "^0.1.24", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/scout": "^10.9", "laravel/tinker": "^2.9", "laravel/ui": "^4.2", "league/csv": "^9.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "meilisearch/meilisearch-php": "^1.3", "openai-php/laravel": "^0.9.1", "phpseclib/phpseclib": "~3.0", "pxlrbt/filament-environment-indicator": "^2.0", "ryangjchandler/filament-progress-column": "^1.0", "spatie/browsershot": "^4.0", "spatie/data-transfer-object": "^3.8", "spatie/laravel-data": "^4.0", "spatie/laravel-event-sourcing": "^7.7", "spatie/laravel-google-fonts": "^1.2", "spatie/laravel-medialibrary": "^11.5", "spatie/laravel-permission": "^6.9", "spatie/laravel-ray": "^1.29", "spatie/laravel-robots-middleware": "^1.3", "spatie/laravel-sitemap": "^7.2", "spatie/laravel-stripe-webhooks": "^3.2", "spatie/laravel-webhook-client": "^3.1", "stripe/stripe-php": "^7.84", "symfony/console": "^7.0", "symfony/http-client": "^6.1", "symfony/mailgun-mailer": "^6.1", "symfony/postmark-mailer": "^6.1", "symfony/process": "^7.0", "taxjar/taxjar-php": "^1.10", "timokoerber/laravel-one-time-operations": "^1.4", "valentin-morice/filament-json-column": "^1.3", "z3d0x/filament-fabricator": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "pestphp/pest": "^2.34", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}, "patches": {"kalnoy/nestedset": []}, "google/apiclient-services": ["ShoppingContent"]}, "autoload": {"files": ["app/helpers.php"], "exclude-from-classmap": ["vendor/filament/notifications/src/DatabaseNotification.php"], "psr-4": {"App\\": "app/", "Database\\": "database/", "Database\\Migrations\\": "database/migrations/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Domains\\": "src/Domains", "Filament\\Notifications\\": "app/Overrides/Extensions/Filament/Notifications/"}, "classmap": ["database/migrations", "database/seeders", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"pre-autoload-dump": ["Google\\Task\\Composer::cleanup"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}