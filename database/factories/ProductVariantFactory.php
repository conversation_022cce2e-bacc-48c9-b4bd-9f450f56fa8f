<?php

namespace Database\Factories;

use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;
use function make_slug;
use function rand;

class ProductVariantFactory extends Factory {
	/**
	 * The name of the factory's corresponding model.
	 *
	 * @var string
	 */
	protected $model = ProductVariant::class;

	/**
	 * Define the model's default state.
	 *
	 * @return array
	 */
	public function definition(): array {
		$name = implode(' ', $this->faker->words( rand( 2, 4 ) ));

		return [
			'code'              => make_slug( $name ),
			'name'              => $name,
			'short_description' => $this->faker->paragraph( 3 ),
			'weight'            => (string) $this->faker->randomFloat( 2, .5, 200 ),
			'width'             => (string) $this->faker->randomFloat( 2, .5, 120 ),
			'height'            => (string) $this->faker->randomFloat( 2, .5, 120 ),
			'depth'             => (string) $this->faker->randomFloat( 2, .5, 120 ),
			'price'             => convert_currency( $this->faker->randomFloat( 2, .5, 2000 ) ),
			'price_unit'        => 'EA',
		];
	}
}
